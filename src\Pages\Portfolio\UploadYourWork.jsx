import React from 'react'
import { BiText } from 'react-icons/bi'
import { CiGrid42 } from 'react-icons/ci'
import { FaImage } from 'react-icons/fa'

const UploadYourWork = () => {
    return (
        <div className='p-6'>
            {/* Header */}
            <div className='flex justify-between mb-3'>
                <h1 className="text-2xl font-bold text-gray-900">Upload Your Work</h1>
            </div>

            <div className='w-full flex flex-col justify-center items-center min-h-[500px]'>
                <h4 className='text-[#888888] text-2xl font-normal mb-6'>Start Building Your Project</h4>
                <div className='flex gap-10 '>
                    <div className='flex flex-col gap-2 items-center'>
                        <div className='bg-[#8E59E2] w-14 h-14 rounded-full flex justify-center items-center'>
                            <FaImage className='text-white'/>
                        </div>
                        <p className='text-purple-500'>Image</p>
                    </div>
                    <div className='flex flex-col gap-2 items-center'>
                        <div className='bg-[#8E59E2] w-14 h-14 rounded-full flex justify-center items-center'>
                            <BiText className='text-white'/>
                        </div>
                        <p className='text-purple-500'>Text</p>
                    </div>
                    <div className='flex flex-col gap-2 items-center'>
                        <div className='bg-[#8E59E2] w-14 h-14 rounded-full flex justify-center items-center'>
                            <CiGrid42 className='text-white'/>
                        </div>
                        <p className='text-purple-500'>Photo Grid</p>
                    </div>
                </div>
            </div>

        </div>
    )
}

export default UploadYourWork
