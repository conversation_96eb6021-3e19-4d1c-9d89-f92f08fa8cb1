import React from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>he<PERSON>ronDown 
} from 'react-icons/hi';

const Navbar = ({ toggleSidebar }) => {
  return (
    <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm border-b border-gray-200 z-30">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left side */}
        <div className="flex items-center space-x-4">
          {/* Menu button - hidden on mobile, shown on lg+ */}
          <button
            onClick={toggleSidebar}
            className="hidden lg:block p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <HiMenu className="w-6 h-6 text-gray-600" />
          </button>

          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div>
            <span className="font-semibold text-gray-900 hidden sm:block">
              Phanom Landing Page
            </span>
          </div>
        </div>

        {/* Center - Search (hidden on mobile) */}

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Navigation links (hidden on mobile) */}
          <div className="hidden lg:flex items-center space-x-6">
            <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
              Service
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
              Hire Indian Talent
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
              Our Portfolio
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
              Case Study
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors">
              Blog
            </a>
          </div>

          {/* Notifications */}
          <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative">
            <HiBell className="w-6 h-6 text-gray-600" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </button>

          {/* Hire Indian Talent button */}
          <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors hidden sm:block">
            Hire Indian Talent
          </button>

          {/* Mobile menu button - shown only on mobile */}
          <button
            onClick={toggleSidebar}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <HiMenu className="w-6 h-6 text-gray-600" />
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
