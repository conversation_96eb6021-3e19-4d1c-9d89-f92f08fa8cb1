import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './Layout/Layout'
import Dashboard from './Pages/Dashboard/Dashboard'


const App = () => {
  return (
     <Router>
      <Routes>
        {/* Routes with layout */}
        <Route element={<Layout />}>
          <Route path="/" element={<Dashboard />} />
        </Route>
        {/* Routes without layout */}
        
      </Routes>
    </Router>
  )
}

export default App
