import React from 'react';
import { 
  HiH<PERSON>,
  HiU<PERSON>,
  <PERSON><PERSON>riefcase,
  HiMail,
  HiCog,
  HiSupport,
  HiX,
  HiChartBar,
  HiDocumentText,
  HiUsers
} from 'react-icons/hi';

const Sidebar = ({ isOpen, toggleSidebar }) => {
  const menuItems = [
    {
      title: 'Overview',
      items: [
        { name: 'Dashboard', icon: HiHome, active: true },
        { name: 'Profile', icon: HiUser },
        { name: 'Portfolio', icon: HiBriefcase },
        { name: 'Message', icon: HiMail },
        { name: 'My Gigs', icon: HiChartBar },
        { name: 'Orders', icon: HiDocumentText },
        { name: 'Earnings', icon: HiChartBar },
        { name: 'Favourite Services', icon: HiUsers },
        { name: 'Support', icon: HiSupport }
      ]
    }
  ];

  return (
    <>
      {/* Sidebar */}
      <aside className={`
        fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] bg-white border-r border-gray-200 transition-all duration-300
        w-64 lg:translate-x-0 ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Close button for mobile */}
          <div className="flex items-center justify-between p-4 lg:hidden">
            <span className="font-semibold text-gray-900">Menu</span>
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100"
            >
              <HiX className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          {/* Menu items */}
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
            {menuItems.map((section, sectionIndex) => (
              <div key={sectionIndex} className="space-y-1">
                {/* Section title */}
                <h3 className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider lg:block hidden">
                  {section.title}
                </h3>
                {isOpen && (
                  <h3 className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider lg:hidden">
                    {section.title}
                  </h3>
                )}
                
                {/* Menu items */}
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon;
                  return (
                    <a
                      key={itemIndex}
                      href="#"
                      className={`
                        flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                        ${item.active
                          ? 'bg-purple-100 text-purple-700 border-r-2 border-purple-700'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }
                        ${!isOpen ? 'justify-center lg:justify-start' : ''}
                      `}
                    >
                      <IconComponent className="w-5 h-5 mr-3" />
                      <span className={`truncate ${isOpen ? '' : 'hidden lg:block'}`}>
                        {item.name}
                      </span>
                    </a>
                  );
                })}
              </div>
            ))}
          </nav>

          {/* Bottom section */}
          <div className="p-3 border-t border-gray-200">
            <a
              href="#"
              className={`
                flex items-center px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors
                ${!isOpen ? 'justify-center lg:justify-start' : ''}
              `}
            >
              <HiCog className="w-5 h-5 mr-3" />
              <span className={`${isOpen ? '' : 'hidden lg:block'}`}>Settings</span>
            </a>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
