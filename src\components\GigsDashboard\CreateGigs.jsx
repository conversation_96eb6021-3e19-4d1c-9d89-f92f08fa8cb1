import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import BasicInformation from "./BasicInformation";
import Description from "./Description";
import FAQSection from "./FAQSection";
import Milestones from "./Milestones";
import GalleryMedia from "./GalleryMedia";
import Review from "./Review";

const steps = ["basic", "description", "faq", "milestones", "gallery", "review"];

const stepLabels = {
  basic: "Basic Information",
  description: "Description",
  faq: "FAQ Section",
  milestones: "Milestones",
  gallery: "Gallery & Media",
  review: "Review",
};

// Validation schema for the entire form
const validationSchema = Yup.object({
  basic: Yup.object({
    title: Yup.string()
      .min(10, "Title must be at least 10 characters")
      .max(80, "Title cannot exceed 80 characters")
      .required("Gig title is required"),
    category: Yup.string().required("Category is required"),
    subcategory: Yup.string().required("Subcategory is required"),
    technology: Yup.string().required("Technology is required"),
    tags: Yup.array().min(1, "At least one tag is required"),
  }),
  description: Yup.object({
    description: Yup.string()
      .min(50, "Description must be at least 50 characters")
      .required("Description is required"),
  }),
  faq: Yup.object({
    faqs: Yup.array().min(1, "At least one FAQ is required"),
  }),
  milestones: Yup.object({
    milestones: Yup.array().min(1, "At least one milestone is required"),
  }),
  gallery: Yup.object({
    images: Yup.array().min(1, "At least one image is required"),
  }),
});

const CreateGigs = () => {
  const [activeStep, setActiveStep] = useState("basic");
  const [completedSteps, setCompletedSteps] = useState([]);
  const navigate = useNavigate();

  // Initial form values
  const initialValues = {
    basic: {
      title: "",
      category: "",
      subcategory: "",
      technology: "",
      tags: [],
      confirmed: false,
    },
    description: {
      description: "",
    },
    faq: {
      faqs: [],
    },
    milestones: {
      milestones: [],
    },
    gallery: {
      images: [],
      videos: [],
    },
  };

  const handleContinue = (step) => {
    const currentIndex = steps.indexOf(step);

    // Mark current step as completed
    if (!completedSteps.includes(step)) {
      setCompletedSteps([...completedSteps, step]);
    }

    // Move forward
    if (currentIndex < steps.length - 1) {
      setActiveStep(steps[currentIndex + 1]);
    }
  };

  const handleBack = (step) => {
    const currentIndex = steps.indexOf(step);
    if (currentIndex > 0) {
      setActiveStep(steps[currentIndex - 1]);
    }
  };

  const handleSubmit = async (values) => {
    try {
      console.log("=== FINAL FORM SUBMISSION ===");
      console.log("Complete Gig Data:", JSON.stringify(values, null, 2));
      console.log("Basic Information:", values.basic);
      console.log("Description:", values.description);
      console.log("FAQ:", values.faq);
      console.log("Milestones:", values.milestones);
      console.log("Gallery:", values.gallery);
      console.log("=== END FORM DATA ===");

      // TODO: Replace with actual API call
      // const response = await fetch('/api/gigs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(values)
      // });

      alert("Gig created successfully! Check console for complete data.");
      navigate("/gigs");
    } catch (error) {
      console.error("Error creating gig:", error);
      alert("Error creating gig. Please try again.");
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize={true}
    >
      {(formikProps) => (
        <div className="min-h-screen bg-gray-50 text-gray-600">
      {/* Navbar */}
      <div className="bg-gray-100 shadow-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <h1 className="text-xl font-semibold">Create Gigs</h1>
          <div className="flex space-x-3">
            <button className="px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-100 text-sm">
              Save
            </button>
            <button className="px-4 py-2 rounded-lg bg-purple-600 text-white hover:bg-purple-700 text-sm">
              Save & Preview
            </button>
          </div>
        </div>

        {/* Step Navigation (non-clickable) */}
        <div className="flex items-center space-x-6 px-6 py-4 text-sm font-medium">
          {steps.map((step, index) => {
            const isActive = activeStep === step;
            const isCompleted = completedSteps.includes(step);

            return (
              <div
                key={step}
                className={`flex items-center space-x-2 cursor-default ${
                  isActive
                    ? "text-purple-600"
                    : isCompleted
                    ? "text-green-600"
                    : "text-gray-400"
                }`}
              >
                <span
                  className={`flex items-center justify-center w-5 h-5 text-xs font-bold rounded-full ${
                    isActive
                      ? "bg-purple-600 text-white"
                      : isCompleted
                      ? "bg-green-600 text-white"
                      : "bg-gray-200"
                  }`}
                >
                  {index + 1}
                </span>
                <span>{stepLabels[step]}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Conditional Step Content */}
      <div className="p-6">
        {activeStep === "basic" && (
          <BasicInformation
            onCancel={() => navigate(-1)}
            onContinue={() => handleContinue("basic")}
            initialData={formikProps.values.basic}
            formikProps={formikProps}
          />
        )}
        {activeStep === "description" && (
          <Description
            onCancel={() => handleBack("description")}
            onContinue={() => handleContinue("description")}
            initialData={formikProps.values.description}
            formikProps={formikProps}
          />
        )}
        {activeStep === "faq" && (
          <FAQSection
            onCancel={() => handleBack("faq")}
            onContinue={() => handleContinue("faq")}
            initialData={formikProps.values.faq}
            formikProps={formikProps}
          />
        )}
        {activeStep === "milestones" && (
          <Milestones
            onCancel={() => handleBack("milestones")}
            onContinue={() => handleContinue("milestones")}
            initialData={formikProps.values.milestones}
            formikProps={formikProps}
          />
        )}
        {activeStep === "gallery" && (
          <GalleryMedia
            onCancel={() => handleBack("gallery")}
            onContinue={() => handleContinue("gallery")}
            initialData={formikProps.values.gallery}
            formikProps={formikProps}
          />
        )}
        {activeStep === "review" && (
          <Review
            onBack={() => handleBack("review")}
            onSubmit={() => formikProps.handleSubmit()}
            gigData={formikProps.values}
          />
        )}
      </div>
    </div>
      )}
    </Formik>
  );
};

export default CreateGigs;
